<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="msapplication-TileColor" content="#00aba9" />
  <meta name="description" content="SenseHawk Digitization Platform for Renewables, Infrastructure & Construction" />
  <meta name="theme-color" content="#ffffff" />

  <title>TaskMapper</title>

  <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#00aba9" />
  <link rel="preconnect" href="https://rsms.me/">
  <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  
  <script>
    window.logger = {
      forceLog: () => {},
      log: () => {},
      error: () => {},
      warn: () => {},
      info: () => {},
      table: () => {},
    }
    
    if (window?.console) {
      if (localStorage.getItem('SH_DEBUG')) {
        window.logger = {
          log: window.console.log.bind(window.console, '[LOG]: %s'),
          error: window.console.error.bind(window.console, '[ERROR]: %s'),
          info: window.console.info.bind(window.console, '[INFO]: %s'),
          warn: window.console.warn.bind(window.console, '[WARN]: %s'),
          table: window.console.table.bind(window.console, '[TABLE]: %s'),
        }
      }
      window.logger.forceLog = window.console.log.bind(window.console, '%s')
    }

    window.markerConfig = {
      project: '647ffdd1d04789e955d40364',
      source: 'snippet',
    }
    const process={env:{}};
  </script>
 <script  defer src="https://maps.googleapis.com/maps/api/js?key=%VITE_APP_GOOGLE_API_KEY%&libraries=places&loading=async&callback=Function.prototype"></script>
</head>

<body class="font-sans">
  <div id="app"></div>
</body>


</html>
