<script setup>
import { capitalize, isEqual, replace } from 'lodash-es';
import HawkTable from '~/common/components/organisms/hawk-table/hawk-table.vue';
import { convertTableUnitsData } from '~/common/utils/common.utils.js';
import { useDashboardStore } from '~/dashboard/store/dashboard.store.js';
import { useDashboardScheduleStore } from '~/dashboard/store/dashboard-schedule.store.js';

const props = defineProps({
  data: {
    type: Object,
  },
  id: {
    type: String,
  },
  // eslint-disable-next-line vue/prop-name-casing
  content_height: {
    type: Number,
  },
});

const $services = inject('$services');

const dashboard_schedule_store = useDashboardScheduleStore();
const dashboard_store = useDashboardStore();

const force_update = ref(0);
const activities = ref([]);
const columns = ref([]);
const loading = ref(false);
const no_data = ref(false);
const payload = ref(null);
const prevent_watcher = ref(false);
const accessorKey = ref(null);
const nested_headers = ref([]);

const height = computed(() => {
  return ((props.data.h || 22) * 20) - 44;
});

const widget_data = computed(() => props.data?.data || {});

const colHeaders = function (index) {
  return columns.value[index].text;
};
async function getReports() {
  loading.value = true;
  activities.value = Object.assign([]);
  columns.value = Object.assign([]);
  nested_headers.value = Object.assign([]);
  payload.value = dashboard_schedule_store.parse_schedule_form_to_server_format(props.data.data);
  accessorKey.value = payload.value?.data?.interval?.name;
  try {
    const { data } = await $services.project_management.get_graph({ body: payload.value });
    if (data.data && Object.keys(data.data).length) {
      generateActivities(data.data);
      no_data.value = false;
    }
    else {
      no_data.value = true;
      columns.value = [];
      activities.value = [];
    }
    loading.value = false;
  }
  catch {
    loading.value = false;
  }
}

function hotSettings() {
  return {
    rowHeaders: true,
    rowHeights: 26,
    viewPortRowRenderingOffset: 100,
    viewportColumnRenderingOffset: 40,
    manualRowMove: false,
    contextMenu: false,
    readOnly: true,
  };
}

function cleanKey(key, values) {
  for (const value of values) {
    if (key.includes(value)) {
      key = key.replace(value, '');
    }
  }
  key = key.replace(/[_-]+$/, '').replace(/^[_-]+/, '');
  return key;
}

function generateActivities(data) {
  columns.value = [
    {
      accessorKey: 'label',
      header: capitalize(accessorKey.value),
      id: 'label',
      size: '150',
      data: 'label',
      text: capitalize(accessorKey.value),
    },
  ];
  const activity_uids = [];
  activities.value = data.reduce((acc, cur) => {
    let activity_object = {};
    activity_object.label = cur.label;

    const activity = Object.values(cur.values).map(act => Object.keys(act).reduce((acc, value, key) => {
      if (!activity_uids.includes(act.uid))
        activity_uids.push(act.uid);
      return { ...acc, [`${value}_${act.uid}`]: act[value] };
    }, {}));

    activity.forEach((item) => {
      activity_object = { ...activity_object, ...item };
    });
    acc.push(activity_object);
    return acc;
  }, []);

  Object.keys(activities.value[0]).forEach((key) => {
    const header = cleanKey(key, activity_uids);
    if (!['uid', 'label', 'name', 'index'].includes(header)) {
      columns.value.push({
        accessorKey: key,
        header: capitalize(header.split('_').join(' ')),
        id: key,
        size: '150',
        text: capitalize(header.split('_').join(' ')),
        data: key,
      });
    }
  });

  const first_level = [{ label: '', colspan: 1 }];
  Object.entries(data[0].values).forEach(([key, value]) => {
    //  removing UID and NAME from the columns length
    first_level.push({ label: value.name, colspan: Object.keys(value).length - 2 });
  });
  const second_level = [...columns.value.map(col => ({ label: col.header, colspan: 1 }))];
  nested_headers.value = payload?.value?.properties?.activity_table_activities?.length ? [first_level, second_level] : [];
}

function columnResized(_resized_column, columns_widths) {
  prevent_watcher.value = true;
  dashboard_store.set_table_column_widths(
    props?.id,
    columns_widths,
  );
}
function updatePrintMap() {
  dashboard_store.update_print_map(props.id, {
    type: widget_data.value.type,
    renderAt: `chart-container-${props?.id}`,
    renderType: 'table',
    width: '100%',
    height: '100%',
    dataFormat: 'json',
    chart_name: widget_data.value.name,
    dimensions: {
      x: props.data.x,
      y: props.data.y,
    },
    dataSource: {
      dashboard_index: props.data.i,
      dataset: activities.value,
      columns: columns.value,
      is_transpose: false,
    },
  });
}

watch(() => props.data.data, async (new_val, old_val) => {
  if (new_val && !isEqual(new_val, old_val) && !prevent_watcher.value) {
    force_update.value++;
    await getReports();
    if (props?.id !== 'preview')
      updatePrintMap();
  }
}, { deep: true });

onMounted(() => {
  loading.value = true;
  dashboard_schedule_store.set_schedules();
  setTimeout(() => {
    getReports();
  }, 1000);
});
</script>

<template>
  <div>
    <div class="w-full h-full">
      <div v-if="$slots['header-title'] || $slots['header-actions']" class="widget-header group">
        <slot name="header-title" />
        <slot name="header-actions" />
      </div>

      <hawk-loader v-if="loading" />
      <div v-else-if="no_data || !activities?.length" class="text-sm font-semiBold w-full" :class="dashboard_store.is_mobile_view ? 'h-[240px] grid place-items-center' : 'mt-8 flex justify-center'">
        {{ $t('No data present') }}
      </div>
      <a v-else-if="activities && activities?.length && columns?.length">

        <div class="w-full scrollbar" :style="{ height: `${content_height || height}px` }">
          <HawkHandsontable
            :key="force_update"
            :apply-read-only-class="true"
            :data="activities"
            :columns="columns"
            :hot-settings="hotSettings()"
            :hot-table-id="id"
            :col-headers="colHeaders"
            :nested-headers="payload?.properties?.activity_table_activities?.length ? nested_headers : []"
            :height="`${content_height || height}px`"
            @after-columns-resized="columnResized"
          />
        </div>
      </a>
    </div>
  </div>
</template>
