const local_testing = 'http://localhost:3001/api';

const BASE_URL = local_testing;

export default {
  bi_schema: {
    url: 'schema',
    baseURL: BASE_URL,
    methods: {
      table: req => ({
        url: `schema/${req.id}/tables`,
        ...(req.query && { params: req.query }),
        ...(req.headers && { headers: req.headers }),
        method: 'GET',
      }),
      columns: req => ({
        url: `schema/${req.id}/tables/${req.table_name}/columns`,
        ...(req.query && { params: req.query }),
        ...(req.headers && { headers: req.headers }),
        method: 'GET',
      }),
      column_values: req => ({
        url: `schema/${req.id}/tables/${req.table_name}/columns/${req.column_name}/values`,
        ...(req.query && { params: req.query }),
        ...(req.headers && { headers: req.headers }),
        method: 'GET',
      }),
    },
  },
  bi_query: {
    url: 'queries',
    baseURL: BASE_URL,
    methods: {
      execute: req => ({
        url: `queries/${req.id}/execute`,
        ...(req.query && { params: req.query }),
        ...(req.headers && { headers: req.headers }),
        data: req.body,
        method: 'POST',
      }),
    },
  },
};
