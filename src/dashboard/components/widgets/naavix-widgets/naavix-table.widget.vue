<script setup>
import { isEqual } from 'lodash-es';
import { nextTick } from 'vue';
import { useDashboardHelpers } from '~/dashboard/composables/dashboard-helpers.composable';
import { useDashboardStore } from '~/dashboard/store/dashboard.store.js';

const props = defineProps({
  data: {
    type: Object,
  },
  id: {
    type: String,
  },
  // eslint-disable-next-line vue/prop-name-casing
  is_editing: {
    type: Boolean,
    default: false,
  },
  canModifyResource: {
    type: Boolean,
    default: false,
  },
});

const dashboard_store = useDashboardStore();

const { deleteWidget } = useDashboardHelpers();

const $services = inject('$services');

const state = reactive({
  is_loading: false,
  hot_data: [],
  hot_columns: [],
});

const chartContainer = ref(null);

const hot_table_height = computed(() => {
  let calculated_height = (state.hot_data.length + 1) * 45 + 7;
  if (calculated_height > (props.data.h * 20))
    calculated_height = (props.data.h * 20);
  return `${calculated_height}px`;
});

async function updatePrintMap() {
  dashboard_store.update_print_map(props.id, {
    type: 'table',
    renderAt: `chart-container-${props?.id}`,
    renderType: 'table',
    width: '100%',
    height: '100%',
    dataFormat: 'json',
    dimensions: {
      x: props.data.x,
      y: props.data.y,
    },
    chart_name: props.data.data.name,
    dataSource: {
      dashboard_index: props.data.i,
      dataset: state.hot_data,
      columns: state.hot_columns.map((column) => { return { ...column, id: column.data }; }),
      is_transpose: false,
    },
  });
}

watch(() => props.data, async (new_val, old_val) => {
  if (!old_val || (!isEqual(new_val, old_val))) {
    state.is_loading = true;
    const response = await $services.ai.get_latest_artifact_data({
      agent_name: props.data.data.agent_name,
      artifact_id: props.data.data.artifact_id,
    });
    state.is_loading = false;
    await nextTick();
    if (response.data.table_data.length) {
      state.hot_data = response.data.table_data;
      state.hot_columns = Object.keys(response.data.table_data[0]).map((entry) => {
        return {
          data: entry,
          header: entry,
        };
      });
    }
    updatePrintMap();
  }
}, { immediate: true }, { deep: true });
</script>

<template>
  <div>
    <div ref="chartContainer" class="h-[inherit]">
      <div v-if="$slots['header-title'] || $slots['header-actions']" class="widget-header group">
        <slot name="header-title" />
        <div v-if="props.is_editing && props.canModifyResource" class="flex items-center justify-around p-1">
          <p class="ml-2 cursor-pointer" @click="deleteWidget(props.data)">
            <icon-hawk-trash-three class="w-5 h-5" />
          </p>
        </div>
        <slot v-else name="header-actions" />
      </div>
      <HawkLoader v-if="state.is_loading" />
      <HawkHandsontable
        v-else-if="state.hot_data.length"
        :key="props.data.uid"
        :hot-table-id="props.data.uid"
        :data="state.hot_data"
        :columns="state.hot_columns"
        :col-headers="state.hot_columns.map(column => column.header)"
        :height="hot_table_height"
        :read-only="true"
      />
      <div v-else class="text-sm font-semiBold w-full h-[240px] grid place-items-center">
        {{ $t('No data present') }}
      </div>
    </div>
  </div>
</template>
