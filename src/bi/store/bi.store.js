import { acceptHMRUpdate, defineStore } from 'pinia';
import { useBILocalStorage } from '~/bi/composables/useBILocalStorage.js';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';

const bi_query_builder = useBIQueryBuilder();
const bi_local_storage = useBILocalStorage();

export const useBiStore = defineStore('bi', {
  state: () => ({
    dashboards: {},
    selected_dashboard: null,
    all_widget_details: {},
    all_tables: [],
    config_change_detection_counter: 1,
    widget_data: [],
    table_metadata: {},
    selected_stage_index: null,
    are_chart_builder_fields_filled: false,
    is_table_dirty: false,
    widget_builder_config: {
      query: {
        selected_table: null,
        sql_query: '',
        selected_database: 'grs_corvera',
        stages: [],
      },
      chart: {},
    },
    chart_builder_data_types: {},
    is_initialized: false,
  }),
  getters: {
    selected_dashboard_details() {
      return this.dashboards[this.selected_dashboard];
    },
  },
  actions: {
    initializeStore() {
      if (this.is_initialized)
        return;

      bi_local_storage.initializeStorage();
      this.dashboards = bi_local_storage.getDashboards();
      this.all_widget_details = bi_local_storage.getWidgets();
      this.selected_dashboard = bi_local_storage.getSelectedDashboard();
      this.is_initialized = true;
    },

    createDashboard(dashboard_data) {
      const new_dashboard = {
        uid: crypto.randomUUID(),
        name: dashboard_data.name,
        widgets: [],
        ...dashboard_data,
      };

      this.dashboards[new_dashboard.uid] = new_dashboard;
      bi_local_storage.createDashboard(new_dashboard);
      return new_dashboard;
    },
    updateDashboard(uid, updates) {
      if (this.dashboards[uid]) {
        this.dashboards[uid] = { ...this.dashboards[uid], ...updates };
        bi_local_storage.updateDashboard(uid, updates);
      }
    },
    deleteDashboard(uid) {
      if (this.dashboards[uid]) {
        delete this.dashboards[uid];
        bi_local_storage.deleteDashboard(uid);

        // If deleted dashboard was selected, select another one
        if (this.selected_dashboard === uid) {
          const remaining_dashboards = Object.keys(this.dashboards);
          this.selected_dashboard = remaining_dashboards.length > 0 ? remaining_dashboards[0] : null;
          if (this.selected_dashboard) {
            bi_local_storage.setSelectedDashboard(this.selected_dashboard);
          }
        }
      }
    },

    setSelectedDashboard(uid) {
      if (this.dashboards[uid]) {
        this.selected_dashboard = uid;
        bi_local_storage.setSelectedDashboard(uid);
      }
    },

    createWidget(widget_id, widget_data) {
      this.all_widget_details[widget_id] = widget_data;
      bi_local_storage.createWidget(widget_id, widget_data);
    },
    updateWidget(widget_id, updates) {
      if (this.all_widget_details[widget_id]) {
        this.all_widget_details[widget_id] = { ...this.all_widget_details[widget_id], ...updates };
        bi_local_storage.updateWidget(widget_id, updates);
      }
    },
    deleteWidget(widget_id) {
      if (this.all_widget_details[widget_id]) {
        delete this.all_widget_details[widget_id];
        bi_local_storage.deleteWidget(widget_id);
      }
    },

    set_chart_builder_data_types() {
      const alias_to_column_mapping = this.alias_to_column_mapping();
      this.chart_builder_data_types = Object.keys(alias_to_column_mapping).reduce((acc, key) => {
        let type = alias_to_column_mapping[key].type;
        switch (alias_to_column_mapping[key].type) {
          case 'string':
          case 'text':
          case 'text_array':
            type = 'text';
            break;
          case 'numeric':
          case 'integer':
          case 'decimal':
          case 'float':
            type = 'numeric';
            break;
          case 'date':
          case 'time':
          case 'timestamp':
            type = 'date';
            break;
          case 'boolean':
            type = 'boolean';
            break;
        }
        acc[key] = type;
        return acc;
      }, {});
    },
    alias_to_column_mapping() {
      if (!this.widget_builder_config.query.stages.length && !this.widget_builder_config.query.stages?.[0]?.value?.columns?.length)
        return {};
      const column_mapping = {};
      this.widget_builder_config.query.stages.forEach(stage => stage.value.columns.forEach((column) => {
        column_mapping[column.alias] = column;
      }));
      return column_mapping;
    },
    async reset_widget_builder() {
      this.widget_builder_config = {
        query: {
          selected_table: null,
          sql_query: '',
          selected_database: 'grs_corvera',
          stages: [],
        },
        chart: {},
      };
      this.widget_data = [];
    },
    async getTables() {
      const response = await this.$services.bi_schema.table({
        id: this.widget_builder_config.query.selected_database,
      });
      this.all_tables = response.data.tables.map(table => ({ label: table.name, columns: table.columns.map(column => ({ ...column, label: column.name, alias: bi_query_builder.constructColumnAlias({ table_name: table.name, column_name: column.name }) })) }));
      return response.data;
    },
    async getTableColumns({ table_name }) {
      const response = await this.$services.bi_schema.columns({
        id: this.widget_builder_config.query.selected_database,
        table_name,
      });
      return response.data;
    },
    async selectTable(table) {
      let { columns } = await this.getTableColumns({ table_name: table.label });
      columns = columns.map(column => ({ ...column, label: column.name, alias: bi_query_builder.constructColumnAlias({ column_name: column.name }) }));
      const columns_with_hierarchy = bi_query_builder.buildHierarchy(columns);
      this.widget_builder_config.query.selected_table = {
        label: table.label,
        columns,
        columns_with_hierarchy,
      };
    },
    async getWidgetData(selected_database, stages) {
      const response = await this.$services.bi_query.execute({
        id: selected_database,
        body: bi_query_builder.getStageConfig(stages),
      });
      return response.data;
    },
    async getColumnValues({ table_name, column_name, query }) {
      const response = await this.$services.bi_schema.column_values({
        id: this.widget_builder_config.query.selected_database,
        table_name,
        column_name,
        query,
      });
      return response.data;
    },
  },
});

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useBiStore, import.meta.hot));
