<script setup>
import { storeToRefs } from 'pinia';
import { nextTick, onBeforeUnmount, onMounted } from 'vue';
import { useModal } from 'vue-final-modal';
import NaavixAddToDashboard from '~/naavix/components/resource-elements/naavix-add-to-dashboard.vue';
import NaavixGraphModal from '~/naavix/components/resource-elements/naavix-graph-modal.vue';
import { useChatScroll, useNaavixHelpers } from '~/naavix/composables/naavix-helpers.composable';
import { useNaavixStore } from '~/naavix/store/naavix.store';

const props = defineProps({
  data: {
    type: String,
  },
  title: {
    type: String,
    default: '',
  },
  description: {
    type: String,
    default: '',
  },
  isPopup: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['scrollToBottom']);

const naavix_store = useNaavixStore();
const { artifacts_map, get_naavix_agent } = storeToRefs(naavix_store);

const { openChartModal } = useNaavixHelpers();
const {
  isAtBottom,
} = useChatScroll();

const $services = inject('$services');
const chartContainer = ref(null);

const chart_modal = useModal({
  component: NaavixGraphModal,
  attrs: {
    onClose() {
      chart_modal.close();
    },
  },
});

const state = reactive({
  graph_uid: null,
  is_loading: false,
  data: null,
  layout: null,
});

const resizeObserver = new ResizeObserver(() => {
  if (state.graph_uid && state.data && state.layout) {
    Plotly.relayout(state.graph_uid, {
      ...state.layout,
      autosize: true,
    });
  }
});

const add_chart_to_dashboard_modal = useModal({
  component: NaavixAddToDashboard,
  attrs: {
    onClose() {
      add_chart_to_dashboard_modal.close();
    },
  },
});

function addChartToDashboard() {
  add_chart_to_dashboard_modal.patchOptions({
    attrs: {
      artifactId: props.data.artifact_id,
      artifactType: 'graph',
      agentName: get_naavix_agent.value.module,
      title: props.title,
    },
  });
  add_chart_to_dashboard_modal.open();
}

onMounted(async () => {
  state.is_loading = true;
  const is_at_bottom = isAtBottom.value;
  if (artifacts_map.value[props.data.artifact_id]) {
    state.data = artifacts_map.value[props.data.artifact_id].data;
    state.layout = artifacts_map.value[props.data.artifact_id].layout;
  }
  else {
    const response = await $services.ai.get_artifact({
      agent_name: get_naavix_agent.value.module,
      artifact_id: props.data.artifact_id,
    });

    state.data = JSON.parse(response.data.chart_config).data;
    state.layout = JSON.parse(response.data.chart_config).layout;

    artifacts_map.value[props.data.artifact_id] = {
      data: state.data,
      layout: state.layout,
    };
  }
  if (!props.data)
    return;
  state.graph_uid = crypto.randomUUID();
  state.is_loading = false;
  await nextTick();

  const final_layout = {
    ...state.layout,
    autosize: true,
  };

  if (!final_layout.xaxis)
    final_layout.xaxis = {};
  if (!final_layout.yaxis)
    final_layout.yaxis = {};
  final_layout.xaxis.automargin = true;
  final_layout.yaxis.automargin = true;

  if (document.getElementById(state.graph_uid))
    await Plotly.newPlot(state.graph_uid, state.data, final_layout);

  if (chartContainer.value) {
    resizeObserver.observe(chartContainer.value);
  }
  await nextTick();
  if (is_at_bottom)
    emit('scrollToBottom');
});

onBeforeUnmount(() => {
  if (chartContainer.value) {
    resizeObserver.unobserve(chartContainer.value);
  }
});
</script>

<template>
  <div class="w-full" :class="props.isPopup ? '' : 'mb-4'">
    <div
      v-if="!props.isPopup"
      class="rounded-lg text-sm font-normal text-gray-900 hover:underline cursor-pointer rounded-tl-none mb-2 prose prose-sm w-fit"
      @click="openChartModal(props.data, props.title, props.description)"
    >
      {{ props.description }}
      <IconHawkShareFour class="inline w-4 h-4 -mt-0.5" />
    </div>
    <div ref="chartContainer" class="border rounded-lg scrollbar">
      <HawkLoader v-if="state.is_loading" />
      <template v-else>
        <div class="flex justify-end">
          <HawkButton v-tippy="$t('Add to dashboard')" icon type="text" class="mt-2 mr-1" size="sm" @click="addChartToDashboard">
            <IconHawkPinTwo />
          </HawkButton>
        </div>
        <div :id="state.graph_uid" class="m-1 min-w-[600px] max-h-[500px]" />
      </template>
    </div>
  </div>
</template>
