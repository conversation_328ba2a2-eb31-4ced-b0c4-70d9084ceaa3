const BI_STORAGE_KEYS = {
  DASHBOARDS: 'bi_dashboards',
  WIDGETS: 'bi_widgets',
  SELECTED_DASHBOARD: 'bi_selected_dashboard',
};

const DEFAULT_STAGES_DATA = [
  {
    selected_table: {
      label: 'progress_data',
      columns: [
        {
          name: 'ID',
          type: 'text',
          pgType: 'text',
          label: 'ID',
          alias: 'ID',
        },
        {
          name: 'Activity',
          type: 'text',
          pgType: 'text',
          label: 'Activity',
          alias: 'Activity',
        },
        {
          name: 'Subactivity',
          type: 'text',
          pgType: 'text',
          label: 'Subactivity',
          alias: 'Subactivity',
        },
        {
          name: 'Layer',
          type: 'text',
          pgType: 'text',
          label: 'Layer',
          alias: 'Layer',
        },
        {
          name: 'Sublayer',
          type: 'text',
          pgType: 'text',
          label: 'Sublayer',
          alias: 'Sublayer',
        },
        {
          name: 'U<PERSON>',
          type: 'text',
          pgType: 'text',
          label: 'U<PERSON>',
          alias: 'U<PERSON>',
        },
        {
          name: 'Scope',
          type: 'integer',
          pgType: 'bigint',
          label: 'Scope',
          alias: '<PERSON><PERSON>',
        },
        {
          name: 'Completed',
          type: 'integer',
          pgType: 'bigint',
          label: 'Completed',
          alias: 'Completed',
        },
        {
          name: 'Actual Start',
          type: 'date',
          pgType: 'date',
          label: 'Actual Start',
          alias: 'Actual Start',
        },
        {
          name: 'Actual Finish',
          type: 'date',
          pgType: 'date',
          label: 'Actual Finish',
          alias: 'Actual Finish',
        },
        {
          name: 'Remaining',
          type: 'numeric',
          pgType: 'double precision',
          label: 'Remaining',
          alias: 'Remaining',
        },
        {
          name: '% Progress',
          type: 'integer',
          pgType: 'bigint',
          label: '% Progress',
          alias: '% Progress',
        },
        {
          name: 'Status',
          type: 'text',
          pgType: 'text',
          label: 'Status',
          alias: 'Status',
        },
        {
          name: 'Planned Start',
          type: 'timestamp',
          pgType: 'timestamp without time zone',
          label: 'Planned Start',
          alias: 'Planned Start',
        },
        {
          name: 'Planned Finish',
          type: 'timestamp',
          pgType: 'timestamp without time zone',
          label: 'Planned Finish',
          alias: 'Planned Finish',
        },
        {
          name: 'Expected Work rate',
          type: 'integer',
          pgType: 'bigint',
          label: 'Expected Work rate',
          alias: 'Expected Work rate',
        },
        {
          name: 'Est. Finish date',
          type: 'date',
          pgType: 'date',
          label: 'Est. Finish date',
          alias: 'Est. Finish date',
        },
        {
          name: 'Tags',
          type: 'unknown',
          pgType: 'ARRAY',
          label: 'Tags',
          alias: 'Tags',
        },
      ],
      columns_with_hierarchy: [
        {
          name: 'ID',
          type: 'text',
          pgType: 'text',
          label: 'ID',
          alias: 'ID',
        },
        {
          name: 'Activity',
          type: 'text',
          pgType: 'text',
          label: 'Activity',
          alias: 'Activity',
        },
        {
          name: 'Subactivity',
          type: 'text',
          pgType: 'text',
          label: 'Subactivity',
          alias: 'Subactivity',
        },
        {
          name: 'Layer',
          type: 'text',
          pgType: 'text',
          label: 'Layer',
          alias: 'Layer',
        },
        {
          name: 'Sublayer',
          type: 'text',
          pgType: 'text',
          label: 'Sublayer',
          alias: 'Sublayer',
        },
        {
          name: 'UOM',
          type: 'text',
          pgType: 'text',
          label: 'UOM',
          alias: 'UOM',
        },
        {
          name: 'Scope',
          type: 'integer',
          pgType: 'bigint',
          label: 'Scope',
          alias: 'Scope',
        },
        {
          name: 'Completed',
          type: 'integer',
          pgType: 'bigint',
          label: 'Completed',
          alias: 'Completed',
        },
        {
          name: 'Actual Start',
          type: 'date',
          pgType: 'date',
          label: 'Actual Start',
          alias: 'Actual Start',
        },
        {
          name: 'Actual Finish',
          type: 'date',
          pgType: 'date',
          label: 'Actual Finish',
          alias: 'Actual Finish',
        },
        {
          name: 'Remaining',
          type: 'numeric',
          pgType: 'double precision',
          label: 'Remaining',
          alias: 'Remaining',
        },
        {
          name: '% Progress',
          type: 'integer',
          pgType: 'bigint',
          label: '% Progress',
          alias: '% Progress',
        },
        {
          name: 'Status',
          type: 'text',
          pgType: 'text',
          label: 'Status',
          alias: 'Status',
        },
        {
          name: 'Planned Start',
          type: 'timestamp',
          pgType: 'timestamp without time zone',
          label: 'Planned Start',
          alias: 'Planned Start',
        },
        {
          name: 'Planned Finish',
          type: 'timestamp',
          pgType: 'timestamp without time zone',
          label: 'Planned Finish',
          alias: 'Planned Finish',
        },
        {
          name: 'Expected Work rate',
          type: 'integer',
          pgType: 'bigint',
          label: 'Expected Work rate',
          alias: 'Expected Work rate',
        },
        {
          name: 'Est. Finish date',
          type: 'date',
          pgType: 'date',
          label: 'Est. Finish date',
          alias: 'Est. Finish date',
        },
        {
          name: 'Tags',
          type: 'unknown',
          pgType: 'ARRAY',
          label: 'Tags',
          alias: 'Tags',
        },
      ],
    },
    value: {
      columns: [
        {
          name: 'Activity',
          type: 'text',
          pgType: 'text',
          label: 'Activity',
          alias: 'Activity',
          table_name: 'progress_data',
          is_table_column: true,
        },
        {
          name: 'Completed',
          type: 'integer',
          pgType: 'bigint',
          label: 'Completed',
          alias: 'Completed',
          table_name: 'progress_data',
          is_table_column: true,
        },
        {
          name: 'Scope',
          type: 'integer',
          pgType: 'bigint',
          label: 'Scope',
          alias: 'Scope',
          table_name: 'progress_data',
          is_table_column: true,
        },
      ],
      orderBy: [],
      filters: [],
      limit: null,
    },
    tables: [
      {
        label: 'progress_data',
        columns: [
          {
            name: 'ID',
            type: 'text',
            pgType: 'text',
            label: 'ID',
            alias: 'ID',
          },
          {
            name: 'Activity',
            type: 'text',
            pgType: 'text',
            label: 'Activity',
            alias: 'Activity',
          },
          {
            name: 'Subactivity',
            type: 'text',
            pgType: 'text',
            label: 'Subactivity',
            alias: 'Subactivity',
          },
          {
            name: 'Layer',
            type: 'text',
            pgType: 'text',
            label: 'Layer',
            alias: 'Layer',
          },
          {
            name: 'Sublayer',
            type: 'text',
            pgType: 'text',
            label: 'Sublayer',
            alias: 'Sublayer',
          },
          {
            name: 'UOM',
            type: 'text',
            pgType: 'text',
            label: 'UOM',
            alias: 'UOM',
          },
          {
            name: 'Scope',
            type: 'integer',
            pgType: 'bigint',
            label: 'Scope',
            alias: 'Scope',
          },
          {
            name: 'Completed',
            type: 'integer',
            pgType: 'bigint',
            label: 'Completed',
            alias: 'Completed',
          },
          {
            name: 'Actual Start',
            type: 'date',
            pgType: 'date',
            label: 'Actual Start',
            alias: 'Actual Start',
          },
          {
            name: 'Actual Finish',
            type: 'date',
            pgType: 'date',
            label: 'Actual Finish',
            alias: 'Actual Finish',
          },
          {
            name: 'Remaining',
            type: 'numeric',
            pgType: 'double precision',
            label: 'Remaining',
            alias: 'Remaining',
          },
          {
            name: '% Progress',
            type: 'integer',
            pgType: 'bigint',
            label: '% Progress',
            alias: '% Progress',
          },
          {
            name: 'Status',
            type: 'text',
            pgType: 'text',
            label: 'Status',
            alias: 'Status',
          },
          {
            name: 'Planned Start',
            type: 'timestamp',
            pgType: 'timestamp without time zone',
            label: 'Planned Start',
            alias: 'Planned Start',
          },
          {
            name: 'Planned Finish',
            type: 'timestamp',
            pgType: 'timestamp without time zone',
            label: 'Planned Finish',
            alias: 'Planned Finish',
          },
          {
            name: 'Expected Work rate',
            type: 'integer',
            pgType: 'bigint',
            label: 'Expected Work rate',
            alias: 'Expected Work rate',
          },
          {
            name: 'Est. Finish date',
            type: 'date',
            pgType: 'date',
            label: 'Est. Finish date',
            alias: 'Est. Finish date',
          },
          {
            name: 'Tags',
            type: 'unknown',
            pgType: 'ARRAY',
            label: 'Tags',
            alias: 'Tags',
          },
        ],
        columns_with_hierarchy: [
          {
            name: 'ID',
            type: 'text',
            pgType: 'text',
            label: 'ID',
            alias: 'ID',
          },
          {
            name: 'Activity',
            type: 'text',
            pgType: 'text',
            label: 'Activity',
            alias: 'Activity',
          },
          {
            name: 'Subactivity',
            type: 'text',
            pgType: 'text',
            label: 'Subactivity',
            alias: 'Subactivity',
          },
          {
            name: 'Layer',
            type: 'text',
            pgType: 'text',
            label: 'Layer',
            alias: 'Layer',
          },
          {
            name: 'Sublayer',
            type: 'text',
            pgType: 'text',
            label: 'Sublayer',
            alias: 'Sublayer',
          },
          {
            name: 'UOM',
            type: 'text',
            pgType: 'text',
            label: 'UOM',
            alias: 'UOM',
          },
          {
            name: 'Scope',
            type: 'integer',
            pgType: 'bigint',
            label: 'Scope',
            alias: 'Scope',
          },
          {
            name: 'Completed',
            type: 'integer',
            pgType: 'bigint',
            label: 'Completed',
            alias: 'Completed',
          },
          {
            name: 'Actual Start',
            type: 'date',
            pgType: 'date',
            label: 'Actual Start',
            alias: 'Actual Start',
          },
          {
            name: 'Actual Finish',
            type: 'date',
            pgType: 'date',
            label: 'Actual Finish',
            alias: 'Actual Finish',
          },
          {
            name: 'Remaining',
            type: 'numeric',
            pgType: 'double precision',
            label: 'Remaining',
            alias: 'Remaining',
          },
          {
            name: '% Progress',
            type: 'integer',
            pgType: 'bigint',
            label: '% Progress',
            alias: '% Progress',
          },
          {
            name: 'Status',
            type: 'text',
            pgType: 'text',
            label: 'Status',
            alias: 'Status',
          },
          {
            name: 'Planned Start',
            type: 'timestamp',
            pgType: 'timestamp without time zone',
            label: 'Planned Start',
            alias: 'Planned Start',
          },
          {
            name: 'Planned Finish',
            type: 'timestamp',
            pgType: 'timestamp without time zone',
            label: 'Planned Finish',
            alias: 'Planned Finish',
          },
          {
            name: 'Expected Work rate',
            type: 'integer',
            pgType: 'bigint',
            label: 'Expected Work rate',
            alias: 'Expected Work rate',
          },
          {
            name: 'Est. Finish date',
            type: 'date',
            pgType: 'date',
            label: 'Est. Finish date',
            alias: 'Est. Finish date',
          },
          {
            name: 'Tags',
            type: 'unknown',
            pgType: 'ARRAY',
            label: 'Tags',
            alias: 'Tags',
          },
        ],
      },
    ],
  },
];

const DEFAULT_WIDGETS_DATA = {
  first_widget: {
    name: 'Widget 1',
    config: {
      chart: {
        type: 'column',
        data: {
          category: 'Activity',
          values: ['Completed'],
          stackBy: null,
        },
        series: {
          Completed: {
            name: 'Completed',
            type: 'bar',
            yAxisIndex: 0,
            color: '#4778b8',
            lineColor: '#4778b8',
            lineWidth: 1,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: true,
          compact: false,
          precision: 2,
        },
        axes: {
          categoryName: null,
          valueName: null,
          categoryLabels: 'show',
          valueLabels: 'show',
          valueScale: 'linear',
        },
        reference_lines: [],
      },
      query: {
        selected_table: [{ label: 'progress_data' }],
        selected_database: 'grs_corvera',
        stages: DEFAULT_STAGES_DATA,
      },
    },
  },
  second_widget: {
    name: 'Widget 2',
    config: {
      chart: {
        type: 'line',
        data: {
          category: 'Activity',
          values: ['Completed'],
          stackBy: null,
        },
        series: {
          Completed: {
            name: 'Completed',
            type: 'line',
            yAxisIndex: 0,
            color: '#4778b8',
            lineColor: '#4778b8',
            lineWidth: 1,
            lineStyle: 'solid',
            smooth: false,
            prefix: '',
            suffix: '',
          },
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: true,
          compact: false,
          precision: 2,
        },
        axes: {
          categoryName: null,
          valueName: null,
          categoryLabels: 'show',
          valueLabels: 'show',
          valueScale: 'linear',
        },
        reference_lines: [],
      },
      query: {
        selected_table: [{ label: 'progress_data' }],
        selected_database: 'grs_corvera',
        stages: DEFAULT_STAGES_DATA,
      },
    },
  },
  third_widget: {
    name: 'Widget 3',
    config: {
      chart: {
        type: 'pie',
        data: {
          category: 'Activity',
          values: ['Completed'],
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: true,
          compact: false,
          precision: 2,
        },
        axes: {},
        reference_lines: [],
      },
      query: {
        selected_table: [{ label: 'progress_data' }],
        selected_database: 'grs_corvera',
        stages: DEFAULT_STAGES_DATA,
      },
    },
  },
  fourth_widget: {
    name: 'Widget 4',
    config: {
      chart: {
        type: 'funnel',
        data: {
          category: 'Activity',
          values: ['Completed'],
        },
        layout: {
          title: null,
          subtitle: null,
        },
        legend: {
          show: true,
          position: 'bottom',
        },
        dataValues: {
          show: true,
          compact: false,
          precision: 2,
        },
        axes: {},
        chartSpecific: {
          funnel: {
            labelsAtCenter: false,
            showPercentages: false,
            is3D: false,
            percentOfPrevious: false,
            maintainSlope: false,
          },
        },
      },
      query: {
        selected_table: [{ label: 'progress_data' }],
        selected_database: 'grs_corvera',
        stages: DEFAULT_STAGES_DATA,
      },
    },
  },
  fifth_widget: {
    name: 'Widget 5',
    config: {
      chart: {
        type: 'table',
        columns_map: {
          Activity: {
            key: 'Activity',
            width: null,
            visible: true,
            order_index: 0,
          },
          Scope: {
            key: 'Scope',
            width: null,
            visible: true,
            order_index: 1,
          },
          Completed: {
            key: 'Completed',
            width: null,
            visible: true,
            order_index: 3,
          },
        },
        show_row_headers: false,
        conditional_formatting: [],
      },
      query: {
        selected_table: [{ label: 'progress_data' }],
        selected_database: 'grs_corvera',
        stages: DEFAULT_STAGES_DATA,
      },
    },
  },
};

const DEFAULT_DASHBOARDS = [
  {
    uid: '1',
    name: 'Dashboard 1',
    widgets: [
      { x: 0, y: 0, w: 10, h: 15, i: '0', widget_id: 'first_widget' },
      { x: 0, y: 15, w: 8, h: 10, i: '1', widget_id: 'second_widget' },
      { x: 2, y: 25, w: 4, h: 10, i: '2', widget_id: 'third_widget' },
      { x: 3, y: 35, w: 4, h: 10, i: '3', widget_id: 'fourth_widget' },
      { x: 3, y: 45, w: 4, h: 10, i: '4', widget_id: 'fifth_widget' },
    ],
  },
  {
    uid: '2',
    name: 'Dashboard 2',
    widgets: [
      { x: 0, y: 0, w: 10, h: 10, i: '1', widget_id: 'second_widget' },
      { x: 0, y: 10, w: 10, h: 15, i: '0', widget_id: 'third_widget' },
    ],
  },
];

export function useBILocalStorage() {
  function safeParseJSON(key, defaultValue = null) {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    }
    catch {
      return defaultValue;
    }
  }

  function safeSetJSON(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    }
    catch {
      return false;
    }
  }

  function initializeStorage() {
    const existing_dashboards = safeParseJSON(BI_STORAGE_KEYS.DASHBOARDS);
    const existing_widgets = safeParseJSON(BI_STORAGE_KEYS.WIDGETS);

    if (!existing_dashboards) {
      const dashboards_obj = {};
      DEFAULT_DASHBOARDS.forEach((dashboard) => {
        dashboards_obj[dashboard.uid] = dashboard;
      });
      safeSetJSON(BI_STORAGE_KEYS.DASHBOARDS, dashboards_obj);
    }

    if (!existing_widgets) {
      safeSetJSON(BI_STORAGE_KEYS.WIDGETS, DEFAULT_WIDGETS_DATA);
    }

    const selected_dashboard = localStorage.getItem(BI_STORAGE_KEYS.SELECTED_DASHBOARD);
    if (!selected_dashboard && DEFAULT_DASHBOARDS.length > 0) {
      localStorage.setItem(BI_STORAGE_KEYS.SELECTED_DASHBOARD, DEFAULT_DASHBOARDS[0].uid);
    }
  }

  // Dashboard operations
  function getDashboards() {
    return safeParseJSON(BI_STORAGE_KEYS.DASHBOARDS, {});
  }

  function saveDashboards(dashboards) {
    return safeSetJSON(BI_STORAGE_KEYS.DASHBOARDS, dashboards);
  }

  function createDashboard(dashboard) {
    const dashboards = getDashboards();
    dashboards[dashboard.uid] = dashboard;
    return saveDashboards(dashboards);
  }

  function updateDashboard(uid, updates) {
    const dashboards = getDashboards();
    if (dashboards[uid]) {
      dashboards[uid] = { ...dashboards[uid], ...updates };
      return saveDashboards(dashboards);
    }
    return false;
  }

  function deleteDashboard(uid) {
    const dashboards = getDashboards();
    if (dashboards[uid]) {
      delete dashboards[uid];
      return saveDashboards(dashboards);
    }
    return false;
  }

  // Widget operations
  function getWidgets() {
    return safeParseJSON(BI_STORAGE_KEYS.WIDGETS, {});
  }

  function saveWidgets(widgets) {
    return safeSetJSON(BI_STORAGE_KEYS.WIDGETS, widgets);
  }

  function createWidget(widget_id, widget_data) {
    const widgets = getWidgets();
    widgets[widget_id] = widget_data;
    return saveWidgets(widgets);
  }

  function updateWidget(widget_id, updates) {
    const widgets = getWidgets();
    if (widgets[widget_id]) {
      widgets[widget_id] = { ...widgets[widget_id], ...updates };
      return saveWidgets(widgets);
    }
    return false;
  }

  function deleteWidget(widget_id) {
    const widgets = getWidgets();
    if (widgets[widget_id]) {
      delete widgets[widget_id];
      return saveWidgets(widgets);
    }
    return false;
  }

  // Selected dashboard operations
  function getSelectedDashboard() {
    return localStorage.getItem(BI_STORAGE_KEYS.SELECTED_DASHBOARD);
  }

  function setSelectedDashboard(uid) {
    localStorage.setItem(BI_STORAGE_KEYS.SELECTED_DASHBOARD, uid);
  }

  // Clear all BI data from localStorage
  function clearAllData() {
    Object.values(BI_STORAGE_KEYS).forEach((key) => {
      localStorage.removeItem(key);
    });
    window.location.reload();
  }

  return {
    // Initialization
    initializeStorage,

    // Dashboard operations
    getDashboards,
    saveDashboards,
    createDashboard,
    updateDashboard,
    deleteDashboard,

    // Widget operations
    getWidgets,
    saveWidgets,
    createWidget,
    updateWidget,
    deleteWidget,

    // Selected dashboard
    getSelectedDashboard,
    setSelectedDashboard,

    // Utility
    clearAllData,
  };
}
