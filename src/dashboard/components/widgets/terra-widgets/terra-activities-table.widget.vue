<script setup>
import { includes, isEqual, sortBy, toString, uniqBy } from 'lodash-es';

import { watch } from 'vue';
import { convertTableUnitsData } from '~/common/utils/common.utils.js';

import { useDashboardStore } from '~/dashboard/store/dashboard.store.js';

import { useDashboardTerraStore } from '~/dashboard/store/dashboard-terra.store.js';

const props = defineProps({
  data: {
    type: Object,
  },
  id: {
    type: String,
  },
  // eslint-disable-next-line vue/prop-name-casing
  content_height: {
    type: Number,
  },
  activeSelectedRange: {
    type: Array,
    default: () => [],
  },
});

const $t = inject('$t');

const $services = inject('$services');

const dashboard_selected_range = inject('dashboardSelectedRange');

const dashboard_store = useDashboardStore();
const dashboard_terra_store = useDashboardTerraStore();

const HEADER_KEY_MAP = {
  'total': $t('Total (scope)'),
  'actual': $t('Actual'),
  'remaining': $t('Remaining'),
  '%_actual': $t('% Actual'),
  '%_remaining': $t('% Remaining'),
  'daily_progress': $t('Today'),
  'this_week': $t('This week'),
  'this_month': $t('This month'),
  'this_year': $t('This year'),
  'dashboard_active_selected_range': $t('Selected range'),
};

const loading = ref(false);
const activities = ref([]);
const print_activities = ref([]);
const columns = ref([]);
const print_columns = ref([]);
const nested_headers = ref([]);
const payload = ref(null);
const forceUpdate = ref(0);
const no_data = ref(false);
const prevent_watcher = ref(false);
const handsontable_map = ref({});

async function getReports() {
  loading.value = true;
  payload.value = dashboard_terra_store.parse_terra_form_to_server_format(props.data.data);
  try {
    const { data } = await $services.terra_view_service.get_activities_table({ body: payload.value });

    if (Object.keys(data?.data)?.length) {
      no_data.value = false;
      if (props.data.data.transpose)
        generateTransposeActivities(data.data);
      else
        generateActivities(data.data);
      loading.value = false;
    }
    else {
      no_data.value = true;
      columns.value = [];
      activities.value = [];
      loading.value = false;
    }

    forceUpdate.value++;
  }
  catch (err) {
    loading.value = false;
    logger.log({ err });
  }
}

const columns_widths_map = computed(() => props.data.data.columns_widths || {});

const field_name_map = computed(() => {
  const res = {};
  props.data.data?.workprogress_fields?.forEach((f) => {
    res[f.column] = f.name;
  });

  return res;
});

function getFieldName(key, value) {
  if (key === 'name')
    return 'Activity';

  const field_name = field_name_map.value[key] || 'Untitled';

  if (value.field_units)
    return `${field_name} (${value.field_units})`;

  return field_name;
}

function getTransposeFieldName(data) {
  const field_name = data?.name || 'Untitled';
  if (data?.field_units)
    return `${field_name} (${data.field_units})`;
  else if (data?.units)
    return `${field_name} (${data.units})`;

  return field_name;
}

function getActivityCellValue(data, key) {
  if (!props.data.data.prettify_units) {
    return data.values[key];
  }
  else
    if (includes(key, '%')) {
      let val = data.values[key];
      if (Math.ceil(val) !== val)
        val = val.toFixed(2);
      return `${val}%`;
    }
  return convertTableUnitsData(data.values[key], data.field_units);
}

function generateActivities(data) {
  const activity_array = [];
  const column_array = [];
  const first_level = [];
  const second_level = [];

  Object.values(data).forEach((value) => {
    const current_item = {};
    for (const [nestedKey, nestedValue] of Object.entries(value)) {
      if (nestedKey !== 'uid') {
        const text = getFieldName(nestedKey, nestedValue);
        if (!(nestedValue?.values && Object.keys(nestedValue.values).length)) {
          first_level.push({ label: '', colspan: 1 });
          second_level.push({ label: text, colspan: 1 });
          column_array.push({
            data: nestedKey,
            text,
            header: text,
            ...(nestedKey === 'name' ? { width: columns_widths_map.value.name || 400 } : {}),
            id: nestedKey,
            width: columns_widths_map.value[nestedKey] || 150,
            size: columns_widths_map.value[nestedKey] || 150,
          });
        }
        else {
          first_level.push({ label: text, colspan: Object.keys(nestedValue.values).length });
          Object.keys(nestedValue.values).forEach((child) => {
            const col_id = `${child}-${nestedKey}`;
            second_level.push({ label: child === 'id' ? 'Activity id' : HEADER_KEY_MAP[child], colspan: 1 });
            column_array.push({
              data: col_id,
              id: col_id,
              text: child === 'id' ? 'Activity id' : HEADER_KEY_MAP[child],
              header: child === 'id' ? 'Activity id' : HEADER_KEY_MAP[child],
              width: columns_widths_map.value?.[col_id] || '150',
              size: columns_widths_map.value?.[col_id] || '150',
            });
          });
        }
      }
      if (nestedValue.values) {
        Object.keys(nestedValue.values).forEach((nestedItem) => {
          current_item[`${nestedItem}-${nestedKey}`] = getActivityCellValue(nestedValue, nestedItem);
        });
      }

      else {
        current_item[nestedKey] = props.data.data.prettify_units ? convertTableUnitsData(nestedValue, nestedValue.field_units) : nestedValue;
      }
    }
    activity_array.push(current_item);
  });

  if (props.data.data.grand_total) {
    activity_array.push({
      disableEdit: true,
      isSummaryRow: true,
    });
  }

  activities.value = sortBy(activity_array, ['name']);
  nested_headers.value = [first_level, second_level];
  columns.value = uniqBy(column_array, c => c.id);
  generatePrintColumns(data);
}

function generatePrintColumns(data) {
  const column_array = [];

  Object.values(data).forEach((value) => {
    for (const [nestedKey, nestedValue] of Object.entries(value)) {
      if (nestedKey !== 'uid') {
        column_array.push({
          accessorKey: nestedValue?.values && Object.keys(nestedValue.values).length === 1 ? `${Object.keys(nestedValue?.values)[0]}-${nestedKey}` : nestedKey,
          header: getFieldName(nestedKey, nestedValue),
          ...(nestedKey === 'name' ? { size: columns_widths_map.value.name || 400 } : {}),
          id: nestedKey,
          ...(nestedValue?.values && Object.keys(nestedValue.values).length > 1
            ? {
                columns: Object.keys(nestedValue.values).map((child) => {
                  const col_id = `${child}-${nestedKey}`;
                  return {
                    accessorKey: col_id,
                    id: col_id,
                    header: child === 'id' ? 'Activity id' : HEADER_KEY_MAP[child],
                    size: columns_widths_map.value?.[col_id] || '150',
                  };
                },
                ),
              }
            : {
                size: columns_widths_map.value?.[nestedKey] || '150',
              }),

        });
      }
    }
  });

  print_columns.value = uniqBy(column_array, c => c.id);
}
function generateTransposeActivities(data) {
  const first_level = [{ label: '', colspan: 1 }];
  const second_level = [{ label: 'Field', colspan: 1 }];

  const column_array = [{
    data: 'name',
    id: 'name',
    text: 'Field',
    header: 'Field',
    width: columns_widths_map.value.name || 400,
    size: columns_widths_map.value.name || 400,
  }];

  const sorted_data = sortBy(Object.values(data), ['name']);
  sorted_data.forEach((value) => {
    let second_level_temp = [];

    for (const [_nestedKey, nestedValue] of Object.entries(value)) {
      if (nestedValue.values) {
        Object.keys(nestedValue.values).forEach((nestedCol) => {
          const col_id = `${nestedCol}-${value.uid}`;
          second_level_temp.push({ label: nestedCol === 'id' ? 'Activity id' : HEADER_KEY_MAP[nestedCol], colspan: 1, id: col_id });
          column_array.push({
            data: col_id,
            id: col_id,
            text: nestedCol === 'id' ? 'Activity id' : HEADER_KEY_MAP[nestedCol],
            header: nestedCol === 'id' ? 'Activity id' : HEADER_KEY_MAP[nestedCol],
            width: columns_widths_map.value[col_id] || 150,
            size: columns_widths_map.value[col_id] || 150,
          });
        });
      }
    }
    second_level_temp = uniqBy(second_level_temp, c => c.id);
    first_level.push({ label: value === 'name' ? 'Activity' : value.name, colspan: second_level_temp.length, id: value.name });
    second_level.push(...second_level_temp);
  });

  const fields = Object.keys(data).reduce((acc, key) => {
    Object.keys(data[key]).forEach((keyItem) => {
      if (!acc.present[keyItem] && typeof data[key][keyItem] === 'object') {
        acc.present = { ...acc.present, [keyItem]: keyItem };
        return acc.list.push(keyItem);
      }
    });
    return acc;
  }, {
    list: [],
    present: {},
  });

  const activity_array_better = fields.list.reduce((acc, fieldKey) => {
    const fieldData = {};
    Object.keys(data).forEach((dataKey) => {
      const dataItem = data[dataKey][fieldKey];
      fieldData.name = getTransposeFieldName(dataItem);
      Object.keys(dataItem?.values ?? {}).forEach((valueKey) => {
        fieldData[`${valueKey}-${dataKey}`] = getActivityCellValue(dataItem, valueKey);
      });
    });
    acc.push(fieldData);
    return acc;
  }, []);

  activities.value = activity_array_better;

  if (props.data.data.grand_total) {
    const summaryRow = {
      name: 'Total',
      disableEdit: true,
      isSummaryRow: true,
    };

    activities.value.push(summaryRow);
  }

  const name_column = column_array[0];
  const activity_columns = uniqBy(column_array, c => c.id).slice(1) || [];
  columns.value = [
    name_column,
    ...activity_columns,
  ];

  nested_headers.value = [first_level, second_level];
  generateTransposePrintColumns(data);
}

function generateTransposePrintColumns(data) {
  const column_array = [{
    accessorKey: 'name',
    id: 'name',
    header: 'Field',
    size: columns_widths_map.value.name || 400,
  }];
  Object.values(data).forEach((value) => {
    const current_column = {
      accessorKey: value.name,
      header: value === 'name' ? 'Activity' : value.name,
      id: value.name,
      columns: [],
    };

    for (const [_nestedKey, nestedValue] of Object.entries(value)) {
      if (nestedValue.values) {
        current_column.columns = Object.keys(nestedValue.values).map((nestedCol) => {
          const col_id = `${nestedCol}-${value.uid}`;
          return {
            accessorKey: col_id,
            id: col_id,
            header: nestedCol === 'id' ? 'Activity id' : HEADER_KEY_MAP[nestedCol],
            size: columns_widths_map.value[col_id] || 150,
          };
        });
      }

      column_array.push(current_column);
    }
  });

  const fields = Object.keys(data).reduce((acc, key) => {
    Object.keys(data[key]).forEach((keyItem) => {
      if (!acc.present[keyItem] && typeof data[key][keyItem] === 'object') {
        acc.present = { ...acc.present, [keyItem]: keyItem };
        return acc.list.push(keyItem);
      }
    });
    return acc;
  }, {
    list: [],
    present: {},
  });

  const activity_array_better = fields.list.reduce((acc, fieldKey) => {
    const fieldData = {};
    Object.keys(data).forEach((dataKey) => {
      const dataItem = data[dataKey][fieldKey];
      fieldData.name = getTransposeFieldName(dataItem);
      Object.keys(dataItem?.values ?? {}).forEach((valueKey) => {
        fieldData[`${valueKey}-${dataKey}`] = getActivityCellValue(dataItem, valueKey);
      });
    });
    acc.push(fieldData);
    return acc;
  }, []);

  print_activities.value = activity_array_better;
  const name_column = column_array[0];
  const activity_columns = uniqBy(column_array, c => c.id).slice(1) || [];
  print_columns.value = [
    name_column,
    ...sortBy(activity_columns, ['header']),
  ];
}

const height = computed(() => {
  return ((props.data.h || 22) * 20) - 44;
});
function hotSettings() {
  return {
    rowHeaders: true,
    rowHeights: 26,
    viewPortRowRenderingOffset: 100,
    viewportColumnRenderingOffset: 40,
    dropdownMenu: false,
    contextMenu: false,
    filters: false,
    manualColumnResize: props.id === 'preview' || dashboard_store.is_editing_dashboard,
    manualRowMove: false,
    manualColumnMove: false,
    readOnly: true,
  };
}

const colHeaders = function (index) {
  return columns.value[index].text;
};

function columnSummaryConfig() {
  if (!activities.value?.length || !columns.value?.length) {
    return { show: false };
  }

  return {
    show: true,
    type: 'column',
    summaryFunction: columns.value.map((field, index) => ({
      type: 'custom',
      destinationRow: 0,
      destinationColumn: index,
      reversedRowCoords: true,
      customFunction() {
        if (!activities.value?.length)
          return 0;
        if (index === 0) {
          return 'Summary';
        }
        else {
          if (field.data && field.data.startsWith('%_actual')) {
            const totalActual = activities.value.reduce((acc, activity) => {
              const actualKey = field.data.replace('%_actual', 'actual');
              const value = Number(activity[actualKey]) || 0;
              return acc + value;
            }, 0);

            const totalScope = activities.value.reduce((acc, activity) => {
              const totalKey = field.data.replace('%_actual', 'total');
              const value = Number(activity[totalKey]) || 0;
              return acc + value;
            }, 0);

            return totalScope > 0 ? Math.round((totalActual / totalScope) * 10000) / 100 : 0;
          }
          else if (field.data && field.data.startsWith('%_remaining')) {
            const totalRemaining = activities.value.reduce((acc, activity) => {
              const remainingKey = field.data.replace('%_remaining', 'remaining');
              const value = Number(activity[remainingKey]) || 0;
              return acc + value;
            }, 0);

            const totalScope = activities.value.reduce((acc, activity) => {
              const totalKey = field.data.replace('%_remaining', 'total');
              const value = Number(activity[totalKey]) || 0;
              return acc + value;
            }, 0);

            return totalScope > 0 ? Math.round((totalRemaining / totalScope) * 10000) / 100 : 0;
          }
          else {
            const total = activities.value.reduce((acc, activity) => {
              const value = activity[field.data];
              return acc + (Number(value) || 0);
            }, 0);
            return Math.round(total * 100) / 100;
          }
        }
      },
    })),
    summaryRowsLength: 1,
  };
}

function updatePrintMap() {
  dashboard_store.update_print_map(props.id, {
    type: props.data.data.type,
    renderAt: `chart-container-${props?.id}`,
    renderType: 'table',
    width: '100%',
    height: '100%',
    dataFormat: 'json',
    chart_name: props.data.data.name,
    dashboard_selected_range,
    dimensions: {
      x: props.data.x,
      y: props.data.y,
    },
    dataSource: {
      columns: print_columns.value,
      activities: print_activities.value.length ? print_activities.value : activities.value,
      dataset: print_activities.value.length ? print_activities.value : activities.value,
      is_transpose: props.data.data.transpose === true,
      dashboard_index: props.data.i,
      is_new_pivot_chart: props.data.data.chart === 'workflow_pivot_table',
    },
  });
  dashboard_store.update_new_print_map((props.data?.data?.name || 'untitled'), {
    widget_type: 'table',
    type: props.data?.data?.type,
    dataSource: parseNewPrintData(activities.value),
  });
}

function parseNewPrintData(data) {
  const accessor_keys = [];
  columns.value.forEach((c) => {
    if (c.columns) {
      c.columns.forEach((cc) => {
        accessor_keys.push(cc.data);
      });
    }
    else {
      accessor_keys.push(c.data);
    }
  });
  const parsed_data = [];
  data.forEach((d) => {
    parsed_data.push(accessor_keys.map(k => toString(d[k])));
  });
  return parsed_data;
}

function columnResized(columns_widths) {
  // prevents the table from rerendering
  prevent_watcher.value = true;
  const columns_widths_by_key = columns.value.reduce((acc, col, idx) => {
    acc[col.data || col.id] = { size: columns_widths[idx], id: col.data || col.id };
    return acc;
  }, {});
  dashboard_store.set_table_column_widths(
    props?.id,
    columns_widths_by_key,
  );
}

watch(() => props.data.data, async (new_val, old_val) => {
  if (new_val && !isEqual(new_val, old_val)) {
    if (prevent_watcher.value) {
      prevent_watcher.value = false;
      return;
    }
    await getReports();
    if (props?.id !== 'preview')
      updatePrintMap();
  }
}, { immediate: true, deep: true });

watch(() => props.activeSelectedRange, async (new_val, old_val) => {
  if (!isEqual(new_val, old_val) && (props?.id !== 'preview')) {
    await getReports();
    updatePrintMap();
  }
});
watch(() => dashboard_store.is_editing_dashboard, async (new_val) => {
  if (handsontable_map.value[props.id]) {
    handsontable_map.value[props.id].updateSettings({
      manualColumnResize: new_val,
    });
  }
}, { immediate: true });
</script>

<template>
  <div>
    <div v-if="$slots['header-title'] || $slots['header-actions']" class="widget-header group">
      <slot name="header-title" />
      <slot name="header-actions" />
    </div>
    <div v-if="no_data" class="text-sm font-semiBold w-full" :class="dashboard_store.is_mobile_view ? 'h-[240px] grid place-items-center' : 'mt-8 flex justify-center'">
      {{ $t('No data present') }}
    </div>

    <hawk-loader v-if="loading" />
    <a v-else-if="activities?.length">
      <div class="w-full scrollbar" :style="{ height: `${content_height || height}px` }">
        <HawkHandsontable
          :apply-read-only-class="false"
          :data="activities"
          :columns="columns"
          :hot-settings="hotSettings()"
          :add-new-row-on-enter="false"
          :hot-table-id="id"
          :col-headers="colHeaders"
          :nested-headers="nested_headers"
          :height="`${content_height || height}px`"
          :column-summary-config="props.data.data.grand_total ? columnSummaryConfig() : null"
          @ready="handsontable_map[id] = $event"
          @after-columns-resized="columnResized"
        />
      </div>
    </a>
  </div>
</template>
