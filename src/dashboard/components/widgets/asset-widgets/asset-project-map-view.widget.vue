<script setup>
import DOMPurify from 'dompurify';
import { isEqual } from 'lodash';
import { computed, onMounted, ref, watch } from 'vue';
import { useMap } from '~/common/composables/mapbox/maps';
import { useCommonStore } from '~/common/stores/common.store.js';
import mapLocationsData from '~/dashboard/components/vueform-schema-templates/map-locations.json';

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
});

const common_store = useCommonStore();
const loading = ref(true);
const map = ref(null);

const defaultConfig = {
  center: [0, 20],
  zoom: 2,
  bounds: null,
};

const mapConfig = computed(() => {
  const locationAlias = props.data?.data?.project_location_detail || 'world';
  if (locationAlias === 'world') {
    return defaultConfig;
  }
  return defaultConfig;
});

async function getLocationBounds(locationName) {
  if (!locationName || locationName === 'world') {
    return null;
  }

  try {
    const response = await fetch(
      `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(locationName)}.json?access_token=${mapboxgl.accessToken}&types=country,region&limit=1`,
    );
    const data = await response.json();

    if (data.features && data.features.length > 0) {
      const bbox = data.features[0].bbox;
      if (bbox) {
        return [
          [bbox[0], bbox[1]],
          [bbox[2], bbox[3]],
        ];
      }
    }
  }
  catch (error) {
    console.error('Error getting location bounds:', error);
  }

  return null;
}

function filterAssetsByBounds(assets, bounds) {
  if (!assets || !bounds) {
    return Object.entries(assets);
  }

  return Object.entries(assets).filter(([_uid, asset]) => {
    if (!asset?.location?.coordinates) {
      return false;
    }

    const [lng, lat] = asset.location.coordinates;
    const longitude = Number.parseFloat(lng);
    const latitude = Number.parseFloat(lat);

    return longitude >= bounds[0][0] && longitude <= bounds[1][0]
      && latitude >= bounds[0][1] && latitude <= bounds[1][1];
  });
}

async function initMap() {
  const config = mapConfig.value;

  const { initMapbox, loadMapBoxPackage, addMapboxToken } = useMap({}, async (event_data, event_name) => {
    if (event_name === 'loaded') {
      addAssetMarkers();
    }
  });

  await loadMapBoxPackage();
  await addMapboxToken();

  map.value = await initMapbox({
    container_id: `map-${props.id}`,
    style: 'mapbox://styles/mapbox/streets-v11',
    center: config.center,
    zoom: config.zoom,
  });

  map.value.addControl(new mapboxgl.NavigationControl());
}

async function addAssetMarkers() {
  if (!map.value) {
    return;
  }

  const assets = common_store.assets_map;
  if (!assets) {
    return;
  }

  const locationAlias = props.data?.data?.project_location_detail || 'world';

  let locationName = 'World';
  if (locationAlias !== 'world') {
    for (const region of mapLocationsData) {
      if (region.members) {
        for (const country of region.members) {
          if (country.alias === locationAlias) {
            locationName = country.name;
            break;
          }
        }
      }
      if (region.name.toLowerCase() === locationAlias || region.name.toLowerCase().replace(/\s+/g, '') === locationAlias) {
        locationName = region.name;
        break;
      }
    }
  }

  const bounds = await getLocationBounds(locationName);
  const filteredAssets = filterAssetsByBounds(assets, bounds);

  const existingMarkers = document.querySelectorAll(`#map-${props.id} .mapboxgl-marker`);
  existingMarkers.forEach(marker => marker.remove());

  if (bounds) {
    map.value.fitBounds(bounds, { padding: -5 });
  }
  else if (locationAlias === 'world') {
    const coordinates = [];
    filteredAssets.forEach(([_uid, asset]) => {
      if (asset?.location?.coordinates) {
        const [lng, lat] = asset.location.coordinates;
        const longitude = Number.parseFloat(lng);
        const latitude = Number.parseFloat(lat);
        if (!Number.isNaN(longitude) && !Number.isNaN(latitude)) {
          coordinates.push([longitude, latitude]);
        }
      }
    });

    if (coordinates.length > 0) {
      const calculatedBounds = coordinates.reduce((bounds, coord) => {
        return [
          [Math.min(bounds[0][0], coord[0]), Math.min(bounds[0][1], coord[1])],
          [Math.max(bounds[1][0], coord[0]), Math.max(bounds[1][1], coord[1])],
        ];
      }, [[coordinates[0][0], coordinates[0][1]], [coordinates[0][0], coordinates[0][1]]]);

      map.value.fitBounds(calculatedBounds, { padding: 20 });
    }
    else {
      map.value.setCenter(defaultConfig.center);
      map.value.setZoom(defaultConfig.zoom);
    }
  }

  filteredAssets.forEach(([_uid, asset]) => {
    if (!asset?.location?.coordinates) {
      return;
    }

    const [lng, lat] = asset.location.coordinates;
    const longitude = Number.parseFloat(lng);
    const latitude = Number.parseFloat(lat);

    if (Number.isNaN(longitude) || Number.isNaN(latitude)) {
      return;
    }

    const markerEl = document.createElement('div');
    markerEl.className = 'asset-marker';
    markerEl.style.width = '20px';
    markerEl.style.height = '20px';
    markerEl.style.backgroundImage = 'url(/img/icons/location-pin-new.png)';
    markerEl.style.backgroundSize = 'contain';
    markerEl.style.backgroundRepeat = 'no-repeat';
    markerEl.style.cursor = 'pointer';

    const popup = new mapboxgl.Popup({ offset: 25 }).setHTML(`
      <div class="p-2">
        <div class="font-semibold">${DOMPurify.sanitize(asset?.name) || 'Unknown Asset'}</div>
        <div class="text-sm text-gray-600">Location: ${DOMPurify.sanitize(asset?.address?.name) || 'Unknown'}</div>
        <div class="text-xs text-gray-500">Lat: ${latitude.toFixed(4)}, Lng: ${longitude.toFixed(4)}</div>
      </div>
    `);

    new mapboxgl.Marker(markerEl)
      .setLngLat([longitude, latitude])
      .setPopup(popup)
      .addTo(map.value);
  });
}

async function getReports() {
  loading.value = true;

  try {
    if (!map.value) {
      await initMap();
    }
    else {
      addAssetMarkers();
    }
  }
  catch (error) {
    console.error('Error loading map:', error);
  }
  finally {
    setTimeout(() => {
      loading.value = false;
    }, 100);
  }
}

if (props.id === 'preview') {
  watch(() => props.data, (new_val, old_val) => {
    if (new_val && !isEqual(new_val, old_val)) {
      loading.value = true;
      setTimeout(() => {
        getReports();
      }, 1050);
    }
  }, { immediate: true, deep: true });
}
else {
  watch(() => props.data, (new_val, old_val) => {
    if (new_val && !isEqual(new_val, old_val)) {
      loading.value = true;
      setTimeout(() => {
        getReports();
      }, 250);
    }
  }, { immediate: true, deep: true });
}

onMounted(() => {
  getReports();
});
</script>

<template>
  <div>
    <div v-if="$slots['header-title'] || $slots['header-actions']" class="widget-header group">
      <slot name="header-title" />
      <slot name="header-actions" />
    </div>
    <hawk-loader v-if="is_loading" class="!m-2" />
    <div v-else class="asset-project-map-view-widget relative">
      <div :id="`map-${props.id}`" class="w-full h-full" style="height: 400px;" />
    </div>
  </div>
</template>

<style scoped>
.asset-project-map-view-widget {
  height: 100%;
  min-height: 400px;
}

.asset-marker:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}
</style>
