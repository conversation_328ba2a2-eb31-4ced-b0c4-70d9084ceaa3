<script setup>
import { storeToRefs } from 'pinia';
import { useModal } from 'vue-final-modal';
import HawkHandsontable from '~/common/components/organisms/hawk-handsontable/hawk-handsontable.vue';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';
import NaavixAddToDashboard from '~/naavix/components/resource-elements/naavix-add-to-dashboard.vue';
import { useNaavixStore } from '~/naavix/store/naavix.store';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['close']);

const { $services, $t } = useCommonImports();

const naavix_store = useNaavixStore();
const { artifacts_map, get_naavix_agent } = storeToRefs(naavix_store);

const state = reactive({
  is_loading: false,
  hot_instance: null,
  title: '',
  description: '',
  hot_data: [],
  hot_columns: [],
});

const add_chart_to_dashboard_modal = useModal({
  component: NaavixAddToDashboard,
  attrs: {
    onClose() {
      add_chart_to_dashboard_modal.close();
    },
  },
});

const hot_table_height = computed(() => {
  let calculated_height = (state.hot_data.length + 1) * 45 + 7;
  if (calculated_height > 500)
    calculated_height = 500;
  return `${calculated_height}px`;
});

function onHandsOnTableReady(event) {
  state.is_loading = false;
  state.hot_instance = event;
}

function onAddToDashboard() {
  add_chart_to_dashboard_modal.patchOptions({
    attrs: {
      artifactId: props.data.artifact_id,
      artifactType: 'table',
      agentName: get_naavix_agent.value.module,
      title: state.title,
    },
  });
  add_chart_to_dashboard_modal.open();
}

function onExportClicked() {
  const exportPlugin = state.hot_instance.getPlugin('exportFile');
  exportPlugin.downloadFile('csv', {
    bom: false,
    columnDelimiter: ',',
    columnHeaders: true,
    exportHiddenColumns: true,
    exportHiddenRows: true,
    fileExtension: 'csv',
    filename: state.title,
    mimeType: 'text/csv',
    rowDelimiter: '\r\n',
    rowHeaders: true,
  });
}

onMounted(async () => {
  state.is_loading = true;
  if (artifacts_map.value[props.data.artifact_id]) {
    state.hot_columns = artifacts_map.value[props.data.artifact_id].columns;
    state.hot_data = artifacts_map.value[props.data.artifact_id].data;
    state.title = artifacts_map.value[props.data.artifact_id].title;
    state.description = artifacts_map.value[props.data.artifact_id].description;
  }
  else {
    const response = await $services.ai.get_artifact({
      agent_name: get_naavix_agent.value.module,
      artifact_id: props.data.artifact_id,
    });

    state.title = response.data.title;
    state.description = response.data.description;

    if (response.data.table_data.length) {
      state.hot_data = response.data.table_data;
      state.hot_columns = Object.keys(response.data.table_data[0]).map((entry) => {
        return {
          data: entry,
          header: entry,
        };
      });

      artifacts_map.value[props.data.artifact_id] = {
        columns: state.hot_columns,
        data: state.hot_data,
        title: state.title,
        description: state.description,
      };
    }
    else {
      artifacts_map.value[props.data.artifact_id] = {
        columns: [],
        data: [],
        title: state.title,
        description: state.description,
      };
    }
  }
  state.is_loading = false;
});
</script>

<template>
  <HawkModalContainer content_class="!z-[10000] w-[80vw]">
    <div class="col-span-12">
      <HawkModalHeader @close="emit('close')">
        <template #title>
          <div class="font-semibold text-lg">
            {{ state.title }}
          </div>
          <div class="text-sm font-normal text-gray-500">
            {{ state.description }}
          </div>
        </template>
        <template v-if="state.hot_data.length" #right>
          <HawkButton v-tippy="$t('Add to dashboard')" icon type="text" @click="onAddToDashboard">
            <IconHawkPinTwo class="text-gray-600" />
          </HawkButton>
          <HawkButton v-tippy="$t('Export as CSV')" icon type="text" class="ml-3" @click="onExportClicked">
            <IconHawkDownloadOne class="text-gray-600" />
          </HawkButton>
        </template>
      </HawkModalHeader>
      <HawkModalContent>
        <HawkLoader v-if="state.is_loading" />
        <HawkHandsontable
          v-else-if="state.hot_data.length"
          :key="props.data.artifact_id"
          :data="state.hot_data"
          :columns="state.hot_columns"
          :col-headers="state.hot_columns.map(column => column.header)"
          :height="hot_table_height"
          :read-only="true"
          @ready="onHandsOnTableReady"
        />
        <div v-else class="text-sm font-semiBold w-full h-[240px] grid place-items-center">
          {{ $t('No data present') }}
        </div>
      </HawkModalContent>
    </div>
  </HawkModalContainer>
</template>
