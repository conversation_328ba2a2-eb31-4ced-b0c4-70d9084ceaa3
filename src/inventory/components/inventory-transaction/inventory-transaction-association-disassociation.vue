<script setup>
import { useModal } from 'vue-final-modal';
import { Tippy } from 'vue-tippy';
import HawkDeletePopup from '~/common/components/organisms/hawk-delete-popup.vue';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';
import { searchData } from '~/common/utils/common.utils';
import { useInventoryStore } from '~/inventory/store/inventory.store.js';

const props = defineProps({
  transactionDetails: { type: Object, default: () => {} },
});

const emit = defineEmits(['update']);
const { $services, $date, $toast, $t, route } = useCommonImports();
const inventory_store = useInventoryStore();

const state = reactive({
  search: '',
  possible_adjustments: [],
  is_loading: false,
  is_adjustment_loading: null,
});

const filtered_data = computed(() => searchData(state.possible_adjustments, state.search, ['number']));

const { open: openAssociatedTransaction, close: closeAssociatedTransaction, patchOptions: patchAssociatedTransaction } = useModal({ component: HawkDeletePopup });

function removeAssociatedTransactionHandler(item) {
  patchAssociatedTransaction({
    attrs: {
      header: 'Remove associated transaction',
      header_icon: IconHawkDashboardPreviewWarning,
      button_text: 'Remove',
      button_color: 'warning',
      content: `${$t('Removing associated transaction')} ${item.number} ${$t('from')} ${props.transactionDetails.number} ${$t('can affect the reconciliation quantities in this')} ${inventory_store.workflows_map?.[props.transactionDetails?.workflow]?.name}. ${$t('Are you sure you want remove?')}`,
      modalOptions: {
        escToClose: false,
        clickToClose: false,
      },
      onClose() {
        closeAssociatedTransaction();
      },
      confirm: async () => {
        try {
          const { data } = await $services.inventory_adjustments.disassociate_transaction({
            id: item.uid,
          });
          if (data?.message === 'Adjustment disassociated successfully') {
            emit('update');
            closeAssociatedTransaction();
          }
          else {
            $toast({
              title: $t('Something went wrong'),
              text: $t('Please try again'),
              type: 'error',
            });
          }
        }
        catch (error) {
          const { title, message } = inventory_store.get_error_status(error?.error_code) || {};
          $toast({
            title: title || $t('Something went wrong'),
            text: message || $t('Please try again'),
            type: 'error',
          });
        }
      },
    },
  });
  openAssociatedTransaction();
}

function addAssociatedTransactionHandler(item) {
  patchAssociatedTransaction({
    attrs: {
      header: $t('Add associated transaction'),
      header_icon: IconHawkDashboardPreviewWarning,
      button_text: $t('Add'),
      button_color: 'primary',
      content: `${$t('Adding associated transaction')} ${item.number} ${$t('to')} ${props.transactionDetails.number} ${$t('can affect the reconciliation quantities in this')} ${inventory_store.workflows_map?.[props.transactionDetails?.workflow]?.name}. ${$t('Are you sure you want add?')}`,
      modalOptions: {
        escToClose: false,
        clickToClose: false,
      },
      onClose() {
        closeAssociatedTransaction();
      },
      confirm: async () => {
        try {
          const { data } = await $services.inventory_adjustments.associate_transaction({
            id: item.uid,
            body: {
              associated_with: props.transactionDetails.uid,
              association_details: {
                remarks: '',
                attachments: [],
              },
            },
          });
          if (data?.message === 'Adjustment associated successfully') {
            emit('update');
            closeAssociatedTransaction();
          }
          else {
            $toast({
              title: $t('Something went wrong'),
              text: $t('Please try again'),
              type: 'error',
            });
          }
        }
        catch (error) {
          const { title, message } = inventory_store.get_error_status(error?.error_code) || {};
          $toast({
            title: title || $t('Something went wrong'),
            text: message || error?.message || $t('Please try again'),
            type: 'error',
          });
        }
      },
    },
  });
  openAssociatedTransaction();
}

const { open: openErrorAssociatedTransaction, close: closeErrorAssociatedTransaction, patchOptions: patchErrorAssociatedTransaction } = useModal({ component: HawkDeletePopup });
function errorAssociatedTransactionHandler(error) {
  patchErrorAssociatedTransaction({
    attrs: {
      header: $t('Error in adding associated transaction'),
      // TODO: check the icon
      header_icon: IconHawkDashboardPreviewWarning,
      button_text: $t('Dismiss'),
      button_color: 'error',
      content: `${error?.message}`,
      show_cancel_button: false,
      modalOptions: { escToClose: false, clickToClose: false },
      onClose() {
        closeErrorAssociatedTransaction();
      },
      confirm: async () => {
        closeErrorAssociatedTransaction();
      },
    },
  });
  openErrorAssociatedTransaction();
}

async function getPossibleAssociationTransactions() {
  try {
    state.is_loading = true;
    const { data } = await $services.inventory_adjustments.get_possible_association_transactions({
      id: props.transactionDetails?.uid,
    });
    state.possible_adjustments = data.adjustments;
    state.is_loading = false;
  }
  catch (error) {
    state.is_loading = false;
    logger.log('🚀 ~ getPossibleAssociationTransactions ~ error:', error);
  }
}

async function validateAssociate(adjustment, close) {
  try {
    state.is_adjustment_loading = adjustment.uid;
    const { data } = await $services.inventory_adjustments.validate_associate_adjustments({
      id: props.transactionDetails?.uid,
      associate_id: adjustment.uid,
    });
    if (data?.message === 'Adjustment validation successful. The adjustments can be associated.') {
      addAssociatedTransactionHandler(adjustment);
      close();
    }
    state.is_adjustment_loading = null;
  }
  catch ({ data: error }) {
    state.is_adjustment_loading = null;
    errorAssociatedTransactionHandler(error);
  }
}

function openAssociatedAdjustment(transaction) {
  const url = `${window.location.origin}${route.params.asset_id ? `/${route.params.asset_id}` : ''}/inventory/transactions?transaction_number=${transaction.number}`;
  window.open(url, '_blank');
}
</script>

<template :key="transactionDetails?.uid">
  <div class="flex flex-wrap gap-2 items-center">
    <template v-if="transactionDetails?.associated_adjustments?.length">
      <Tippy v-for="associated_adjustment in transactionDetails?.associated_adjustments" :key="associated_adjustment.uid">
        <HawkTag
          :has_background="false" class="cursor-pointer" is_clearable on-hover-show-clear-icon
          @click.stop="openAssociatedAdjustment(associated_adjustment)"
          @clear="removeAssociatedTransactionHandler(associated_adjustment)"
        >
          <div class="flex gap-1" :style="`color:${inventory_store.workflows_map[associated_adjustment?.workflow]?.color}`">
            <IconHawkLinkOne class="size-3.5" />
            {{ associated_adjustment.number }}
          </div>
        </HawkTag>
        <template #content>
          <div class="text-xs">
            <div class="flex">
              <div class="w-[78px]">
                {{ $t('Type') }}:
              </div>
              <div class="w-[200px]">
                {{ inventory_store.workflows_map[associated_adjustment?.workflow]?.name }}
              </div>
            </div>
            <div class="flex">
              <div class="w-[78px]">
                {{ $t('Items') }}:
              </div>
              <div class="w-[200px]">
                <template v-for="item in associated_adjustment?.items" :key="item.uid">
                  <div class="mb-1">
                    {{ item.name }} -> {{ item.quantity }} <InventoryUom v-if="item?.uom" :uom="item?.uom" is_symbol />
                  </div>
                </template>
              </div>
            </div>
            <div class="flex">
              <div class="w-[78px]">
                {{ $t('When') }}:
              </div>
              <div class="w-[200px]">
                {{ $date(associated_adjustment?.association_details?.timestamp, 'L_DATETIME_MED') }}
              </div>
            </div>
            <div class="flex">
              <div class="w-[78px]">
                {{ $t('Created by') }}:
              </div>
              <div class="w-[200px]">
                <HawkMembers :members="associated_adjustment?.association_details?.user" type="only_name" name_classes="!text-xs" />
              </div>
            </div>
          </div>
        </template>
      </Tippy>
    </template>
    <div v-else class="text-xs font-medium text-gray-500">
      {{ $t('Add associated transaction links') }}
    </div>

    <HawkMenu :items="state.possible_adjustments" position="fixed">
      <template #trigger>
        <HawkButton v-tippy="{ content: 'Add an associated transaction' }" icon size="xxs" type="outlined" @click="getPossibleAssociationTransactions()">
          <IconHawkPlus class="text-gray-500 size-3.5" />
        </HawkButton>
      </template>
      <template #content="{ close }">
        <div class="w-[260px] p-1.5">
          <HawkSearchInput v-model="state.search" full_width class="mb-2" />
          <div class="max-h-[200px] overflow-y-auto">
            <HawkLoader v-if="state.is_loading" />
            <div v-else-if="!filtered_data?.length" class="py-10 text-sm text-gray-600 text-center">
              {{ $t("No results found") }}
            </div>
            <template v-else>
              <div
                v-for="item in filtered_data" :key="item.uid" class="p-2 hover:bg-gray-50 text-sm rounded flex items-center justify-between cursor-pointer"
                @click="validateAssociate(item, close)"
              >
                {{ item.number }}
                <IconHawkLoadingTwo v-if="state.is_adjustment_loading === item.uid" class="text-blue-600 animate-spin" />
              </div>
            </template>
          </div>
        </div>
      </template>
    </HawkMenu>
  </div>
</template>
